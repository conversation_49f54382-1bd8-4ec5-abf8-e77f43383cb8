# 加密货币永续合约全自动量化系统 设计文档

## 概述

本系统是一个基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，采用微服务架构设计，前后端分离，支持欧易交易所的模拟盘和实盘交易。系统通过技术指标分析和AI决策引擎实现智能开仓和持仓管理。

**核心设计理念：** 系统采用"数据库配置优先"的设计模式，所有配置信息均存储在SQLite3数据库中，不使用传统的配置文件方式，确保配置的统一管理、动态更新和安全存储。

### 核心技术栈
- **后端：** Node.js + Express.js + SQLite3 + CCXT + technicalindicators
- **前端：** React 18 + Ant Design 5.x + TypeScript
- **AI集成：** DeepSeek API (兼容OpenAI SDK)
- **数据库：** SQLite3 (配置存储) + 内存缓存 (实时数据)
- **通信：** RESTful API + Server-Sent Events (实时推送)

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        UI[React + Ant Design UI]
        Router[React Router]
        State[Redux Toolkit]
    end
    
    subgraph "后端层 (Backend)"
        API[Express.js API Server]
        Auth[认证中间件]
        Scheduler[任务调度器]
    end
    
    subgraph "核心服务层 (Core Services)"
        DataService[数据获取服务]
        IndicatorService[技术指标服务]
        AIService[AI决策服务]
        TradingService[交易执行服务]
        RiskService[风险管理服务]
    end
    
    subgraph "数据层 (Data Layer)"
        SQLite[(SQLite3 配置库)]
        Cache[(内存缓存)]
        Logs[(日志文件)]
    end
    
    subgraph "外部服务 (External Services)"
        OKX[欧易交易所 API]
        DeepSeek[DeepSeek AI API]
    end
    
    UI --> API
    API --> DataService
    API --> TradingService
    DataService --> OKX
    IndicatorService --> AIService
    AIService --> DeepSeek
    TradingService --> OKX
    API --> SQLite
    DataService --> Cache
```

### 模块化设计原则

1. **单一职责原则：** 每个服务模块只负责一个特定功能
2. **数据库配置管理：** 所有配置信息统一存储在SQLite3数据库中，不使用传统配置文件
3. **错误隔离：** 各模块独立的错误处理和恢复机制
4. **可扩展性：** 支持新增交易所、AI模型和技术指标

### 配置管理设计原则

**重要说明：本系统不使用传统的配置文件（如config.js、config.json、.env等），所有配置信息均通过SQLite3数据库进行统一管理。**

#### 配置存储策略
- **数据库优先：** 所有系统配置、交易参数、API密钥等均存储在SQLite3数据库中
- **运行时配置：** 系统启动时从数据库加载配置，运行时可动态更新
- **安全存储：** 敏感信息（如API密钥）在数据库中加密存储
- **版本控制：** 配置变更通过数据库记录时间戳，支持配置历史追踪

## 项目目录结构

### 完整项目目录树
```
/                                    # 项目根目录
├── README.md                             # 项目说明文档
├── package.json                          # Node.js项目配置文件
├── package-lock.json                     # 依赖版本锁定文件
├── .gitignore                           # Git忽略文件配置
├── .eslintrc.js                         # ESLint代码规范配置
├── .prettierrc                          # Prettier代码格式化配置
├── tsconfig.json                        # TypeScript编译配置
├── jest.config.js                       # Jest测试框架配置
├── nodemon.json                         # 开发环境热重载配置
│
├── spec/                                # 项目规格说明文档
│   ├── requirements.md                  # 需求文档
│   ├── design.md                        # 设计文档
│   └── tasks.md                         # 实施计划
│
├── src/                                 # 后端源代码目录
│   ├── app.js                          # Express应用主入口文件
│   ├── server.js                       # 服务器启动文件
│   │
│   ├── config/                         # 配置管理模块（仅包含数据库配置类）
│   │   ├── index.js                    # 配置管理主入口
│   │   ├── database.js                 # 数据库连接和初始化
│   │   └── constants.js                # 系统常量定义
│   │
│   ├── database/                       # 数据库相关文件
│   │   ├── init.sql                    # 数据库初始化SQL脚本
│   │   ├── migrations/                 # 数据库迁移脚本
│   │   │   ├── 001_initial_tables.sql  # 初始表结构
│   │   │   └── 002_add_indexes.sql     # 添加索引
│   │   └── seeds/                      # 初始数据种子文件
│   │       ├── default_config.sql      # 默认系统配置
│   │       ├── trading_params.sql      # 默认交易参数
│   │       └── ai_prompts.sql          # 默认AI提示词模板
│   │
│   ├── models/                         # 数据模型层
│   │   ├── index.js                    # 模型主入口
│   │   ├── SystemConfig.js             # 系统配置模型
│   │   ├── TradingParams.js            # 交易参数模型
│   │   ├── TradingPairs.js             # 交易对配置模型
│   │   └── AIPrompts.js                # AI提示词模板模型
│   │
│   ├── services/                       # 核心业务服务层
│   │   ├── index.js                    # 服务层主入口
│   │   ├── DataService.js              # 数据获取服务
│   │   ├── IndicatorService.js         # 技术指标计算服务
│   │   ├── AIService.js                # AI决策服务
│   │   ├── TradingService.js           # 交易执行服务
│   │   ├── RiskService.js              # 风险管理服务
│   │   ├── ExchangeService.js          # 交易所连接服务
│   │   └── TaskScheduler.js            # 任务调度服务
│   │
│   ├── controllers/                    # API控制器层
│   │   ├── index.js                    # 控制器主入口
│   │   ├── authController.js           # 认证相关控制器
│   │   ├── tradingController.js        # 交易相关控制器
│   │   ├── marketController.js         # 市场数据控制器
│   │   ├── aiController.js             # AI决策控制器
│   │   └── systemController.js         # 系统管理控制器
│   │
│   ├── routes/                         # API路由定义
│   │   ├── index.js                    # 路由主入口
│   │   ├── auth.js                     # 认证路由
│   │   ├── trading.js                  # 交易路由
│   │   ├── market.js                   # 市场数据路由
│   │   ├── ai.js                       # AI决策路由
│   │   └── system.js                   # 系统管理路由
│   │
│   ├── middleware/                     # 中间件
│   │   ├── index.js                    # 中间件主入口
│   │   ├── auth.js                     # 认证中间件
│   │   ├── validation.js               # 数据验证中间件
│   │   ├── errorHandler.js             # 错误处理中间件
│   │   ├── cors.js                     # CORS配置中间件
│   │   └── rateLimit.js                # 请求频率限制中间件
│   │
│   ├── utils/                          # 工具函数
│   │   ├── index.js                    # 工具函数主入口
│   │   ├── logger.js                   # 日志工具
│   │   ├── crypto.js                   # 加密解密工具
│   │   ├── validator.js                # 数据验证工具
│   │   ├── formatter.js                # 数据格式化工具
│   │   └── cache.js                    # 缓存管理工具
│   │
│   └── types/                          # TypeScript类型定义
│       ├── index.ts                    # 类型定义主入口
│       ├── api.ts                      # API相关类型
│       ├── trading.ts                  # 交易相关类型
│       ├── market.ts                   # 市场数据类型
│       └── config.ts                   # 配置相关类型
│
├── frontend/                           # 前端项目目录
│   ├── package.json                    # 前端项目配置
│   ├── package-lock.json               # 前端依赖锁定
│   ├── tsconfig.json                   # 前端TypeScript配置
│   ├── vite.config.ts                  # Vite构建配置
│   ├── index.html                      # HTML入口文件
│   │
│   ├── public/                         # 静态资源目录
│   │   ├── favicon.ico                 # 网站图标
│   │   └── logo.png                    # 应用Logo
│   │
│   ├── src/                            # 前端源代码
│   │   ├── main.tsx                    # React应用入口
│   │   ├── App.tsx                     # 根组件
│   │   ├── index.css                   # 全局样式
│   │   │
│   │   ├── components/                 # 通用组件
│   │   │   ├── Layout/                 # 布局组件
│   │   │   │   ├── index.tsx           # 主布局组件
│   │   │   │   ├── Header.tsx          # 头部组件
│   │   │   │   ├── Sidebar.tsx         # 侧边栏组件
│   │   │   │   └── Footer.tsx          # 底部组件
│   │   │   │
│   │   │   ├── Charts/                 # 图表组件
│   │   │   │   ├── index.tsx           # 图表组件入口
│   │   │   │   ├── CandlestickChart.tsx # K线图组件
│   │   │   │   ├── LineChart.tsx       # 折线图组件
│   │   │   │   └── IndicatorChart.tsx  # 技术指标图组件
│   │   │   │
│   │   │   └── Common/                 # 公共组件
│   │   │       ├── index.tsx           # 公共组件入口
│   │   │       ├── LoadingSpinner.tsx  # 加载动画组件
│   │   │       ├── ErrorBoundary.tsx   # 错误边界组件
│   │   │       └── ConfirmModal.tsx    # 确认对话框组件
│   │   │
│   │   ├── pages/                      # 页面组件
│   │   │   ├── Dashboard/              # 仪表板页面
│   │   │   │   ├── index.tsx           # 仪表板主页面
│   │   │   │   ├── AccountInfo.tsx     # 账户信息组件
│   │   │   │   ├── PositionList.tsx    # 持仓列表组件
│   │   │   │   └── SystemStatus.tsx    # 系统状态组件
│   │   │   │
│   │   │   ├── Trading/                # 交易配置页面
│   │   │   │   ├── index.tsx           # 交易配置主页面
│   │   │   │   ├── RiskParams.tsx      # 风险参数设置
│   │   │   │   └── TradingPairs.tsx    # 交易对选择
│   │   │   │
│   │   │   ├── Monitor/                # 交易监控页面
│   │   │   │   ├── index.tsx           # 监控主页面
│   │   │   │   ├── AIDecisions.tsx     # AI决策展示
│   │   │   │   └── TradingSignals.tsx  # 交易信号展示
│   │   │   │
│   │   │   ├── History/                # 交易历史页面
│   │   │   │   ├── index.tsx           # 历史主页面
│   │   │   │   ├── TradeHistory.tsx    # 交易记录
│   │   │   │   └── ProfitStats.tsx     # 盈亏统计
│   │   │   │
│   │   │   ├── Settings/               # 系统设置页面
│   │   │   │   ├── index.tsx           # 设置主页面
│   │   │   │   ├── ApiKeys.tsx         # API密钥管理
│   │   │   │   └── SystemConfig.tsx    # 系统配置
│   │   │   │
│   │   │   └── Logs/                   # 系统日志页面
│   │   │       ├── index.tsx           # 日志主页面
│   │   │       └── LogViewer.tsx       # 日志查看器
│   │   │
│   │   ├── services/                   # 前端API服务
│   │   │   ├── index.ts                # API服务入口
│   │   │   ├── api.ts                  # HTTP请求封装
│   │   │   ├── auth.ts                 # 认证API
│   │   │   ├── trading.ts              # 交易API
│   │   │   ├── market.ts               # 市场数据API
│   │   │   └── system.ts               # 系统API
│   │   │
│   │   ├── store/                      # Redux状态管理
│   │   │   ├── index.ts                # Store配置
│   │   │   ├── slices/                 # Redux切片
│   │   │   │   ├── authSlice.ts        # 认证状态
│   │   │   │   ├── tradingSlice.ts     # 交易状态
│   │   │   │   ├── marketSlice.ts      # 市场数据状态
│   │   │   │   └── systemSlice.ts      # 系统状态
│   │   │   └── middleware/             # Redux中间件
│   │   │       └── api.ts              # API中间件
│   │   │
│   │   ├── hooks/                      # 自定义React Hooks
│   │   │   ├── index.ts                # Hooks入口
│   │   │   ├── useAuth.ts              # 认证Hook
│   │   │   ├── useTrading.ts           # 交易Hook
│   │   │   ├── useMarketData.ts        # 市场数据Hook
│   │   │   └── useWebSocket.ts         # WebSocket Hook
│   │   │
│   │   ├── utils/                      # 前端工具函数
│   │   │   ├── index.ts                # 工具函数入口
│   │   │   ├── format.ts               # 数据格式化
│   │   │   ├── validation.ts           # 表单验证
│   │   │   └── constants.ts            # 前端常量
│   │   │
│   │   └── types/                      # 前端TypeScript类型
│   │       ├── index.ts                # 类型定义入口
│   │       ├── api.ts                  # API类型
│   │       ├── trading.ts              # 交易类型
│   │       └── ui.ts                   # UI组件类型
│   │
│   └── dist/                           # 前端构建输出目录
│
├── tests/                              # 测试文件目录
│   ├── unit/                           # 单元测试
│   │   ├── services/                   # 服务层测试
│   │   │   ├── DataService.test.js     # 数据服务测试
│   │   │   ├── AIService.test.js       # AI服务测试
│   │   │   └── TradingService.test.js  # 交易服务测试
│   │   │
│   │   ├── models/                     # 模型层测试
│   │   │   ├── SystemConfig.test.js    # 系统配置模型测试
│   │   │   └── TradingParams.test.js   # 交易参数模型测试
│   │   │
│   │   └── utils/                      # 工具函数测试
│   │       ├── crypto.test.js          # 加密工具测试
│   │       └── validator.test.js       # 验证工具测试
│   │
│   ├── integration/                    # 集成测试
│   │   ├── api/                        # API集成测试
│   │   │   ├── auth.test.js            # 认证API测试
│   │   │   ├── trading.test.js         # 交易API测试
│   │   │   └── market.test.js          # 市场数据API测试
│   │   │
│   │   └── database/                   # 数据库集成测试
│   │       ├── config.test.js          # 配置数据库测试
│   │       └── migrations.test.js      # 数据库迁移测试
│   │
│   ├── e2e/                            # 端到端测试
│   │   ├── trading-flow.test.js        # 完整交易流程测试
│   │   ├── ai-decision.test.js         # AI决策流程测试
│   │   └── risk-management.test.js     # 风险管理测试
│   │
│   ├── fixtures/                       # 测试数据
│   │   ├── market-data.json            # 模拟市场数据
│   │   ├── trading-signals.json        # 模拟交易信号
│   │   └── ai-responses.json           # 模拟AI响应
│   │
│   └── helpers/                        # 测试辅助工具
│       ├── database.js                 # 测试数据库工具
│       ├── api-client.js               # 测试API客户端
│       └── mock-exchange.js            # 模拟交易所
│
├── logs/                               # 日志文件目录
│   ├── app.log                         # 应用日志
│   ├── error.log                       # 错误日志
│   ├── trading.log                     # 交易日志
│   └── ai-decisions.log                # AI决策日志
│
├── data/                               # 数据文件目录
│   ├── database.sqlite                 # SQLite数据库文件
│   └── backups/                        # 数据库备份目录
│       ├── database_20250108.sqlite    # 按日期备份
│       └── database_latest.sqlite      # 最新备份
│
└── docs/                               # 项目文档目录
    ├── api/                            # API文档
    │   ├── README.md                   # API文档说明
    │   ├── auth.md                     # 认证API文档
    │   ├── trading.md                  # 交易API文档
    │   └── market.md                   # 市场数据API文档
    │
    ├── deployment/                     # 部署文档
    │   ├── setup.md                    # 环境搭建指南
    │   ├── production.md               # 生产环境部署
    │   └── troubleshooting.md          # 故障排除指南
    │
    └── development/                    # 开发文档
        ├── getting-started.md          # 开发入门指南
        ├── coding-standards.md         # 编码规范
        └── testing-guide.md            # 测试指南
```

### 目录结构设计说明

#### 1. 后端架构 (src/)
- **分层架构：** 采用经典的MVC分层架构，清晰分离关注点
- **服务层：** 核心业务逻辑封装在services目录中，每个服务职责单一
- **数据库层：** 完整的数据库管理，包括初始化、迁移、种子数据
- **配置管理：** config目录仅包含数据库配置类，不存储实际配置数据

#### 2. 前端架构 (frontend/)
- **组件化设计：** 按功能模块组织组件，便于维护和复用
- **状态管理：** 使用Redux Toolkit进行集中状态管理
- **类型安全：** 完整的TypeScript类型定义，确保类型安全

#### 3. 测试架构 (tests/)
- **多层测试：** 单元测试、集成测试、端到端测试全覆盖
- **真实API测试：** 支持使用真实的OKX模拟盘和DeepSeek API进行测试
- **测试数据：** 完整的测试数据和模拟工具

#### 4. 数据管理 (data/)
- **数据库文件：** SQLite数据库文件统一存放
- **备份策略：** 自动化数据库备份机制

#### 5. 日志管理 (logs/)
- **分类日志：** 按功能模块分类存储日志文件
- **日志轮转：** 支持日志文件的自动轮转和清理

#### 6. 文档管理 (docs/)
- **完整文档：** API文档、部署文档、开发文档分类管理
- **维护性：** 文档与代码同步更新，确保文档的时效性

### 关键设计特点

#### 1. 无配置文件设计
- **config/目录：** 仅包含数据库连接和常量定义，不存储实际配置
- **数据库配置：** 所有配置通过SQLite数据库统一管理
- **环境变量：** 仅用于数据库连接等基础设置

#### 2. 模块化和可扩展性
- **服务解耦：** 各服务模块独立，便于单独测试和维护
- **插件架构：** 支持新交易所、AI模型的插件式扩展
- **类型安全：** 完整的TypeScript支持，减少运行时错误

#### 3. 开发和部署友好
- **热重载：** 开发环境支持代码热重载
- **构建优化：** 前端使用Vite进行快速构建
- **测试覆盖：** 完整的测试体系，确保代码质量

## 组件和接口设计

### 后端核心组件

#### 1. 数据获取服务 (DataService) - 纯实时获取，不存储
```javascript
class DataService {
  // 实时获取多时间周期K线数据 (通过CCXT直接获取，不存储)
  async getMultiTimeframeData(symbol, timeframes = ['1m', '5m', '15m', '1h'], limit = 100)

  // 实时获取账户信息 (通过CCXT直接获取)
  async getAccountInfo()

  // 实时获取持仓信息 (通过CCXT直接获取)
  async getPositions()

  // 实时获取订单信息 (通过CCXT直接获取)
  async getOrders(symbol)

  // 清理过期的临时缓存数据
  cleanExpiredCache()
}
```

#### 2. 技术指标服务 (IndicatorService)
```javascript
class IndicatorService {
  // 计算单个时间周期指标
  calculateIndicators(ohlcvData, indicators)
  
  // 计算多时间周期综合指标
  calculateMultiTimeframeIndicators(multiTimeframeData)
  
  // 格式化指标数据为AI输入格式
  formatIndicatorsForAI(indicators)
}
```

#### 3. AI决策服务 (AIService)
```javascript
class AIService {
  // 开仓决策引擎
  async getOpeningDecision(marketData, indicators, accountInfo)
  
  // 持仓决策引擎
  async getPositionDecision(position, marketData, indicators)
  
  // 解析AI响应
  parseAIResponse(response)
}
```

#### 4. 交易执行服务 (TradingService)
```javascript
class TradingService {
  // 执行开仓
  async openPosition(signal, riskParams)
  
  // 执行平仓
  async closePosition(positionId, reason)
  
  // 更新止盈止损
  async updateStopLoss(positionId, newStopLoss)
  
  // 计算仓位大小
  calculatePositionSize(signal, accountInfo, riskParams)
}
```

#### 5. 风险管理服务 (RiskService)
```javascript
class RiskService {
  // 验证开仓风险
  validateOpeningRisk(signal, accountInfo, currentPositions)
  
  // 计算杠杆调整后的止盈止损
  calculateLeveragedStopLoss(entryPrice, leverage, stopLossPercent)
  
  // 检查保证金充足性
  checkMarginRequirement(positionSize, leverage, accountBalance)
}
```

### 前端组件架构

#### 1. 页面组件层次结构
```
src/
├── components/           # 通用组件
│   ├── Layout/          # 布局组件
│   ├── Charts/          # 图表组件
│   └── Common/          # 公共组件
├── pages/               # 页面组件
│   ├── Dashboard/       # 仪表板
│   ├── Trading/         # 交易配置
│   ├── Monitor/         # 交易监控
│   ├── History/         # 交易历史
│   ├── Settings/        # 系统设置
│   └── Logs/           # 系统日志
├── services/            # API服务
├── store/              # Redux状态管理
├── hooks/              # 自定义Hooks
└── utils/              # 工具函数
```

#### 2. 状态管理设计
```javascript
// Redux Store 结构
{
  auth: {
    isAuthenticated: boolean,
    apiKeys: object
  },
  trading: {
    positions: array,
    orders: array,
    balance: object
  },
  market: {
    symbols: array,
    currentData: object
  },
  ai: {
    decisions: array,
    confidence: number
  },
  settings: {
    riskParams: object,
    tradingPairs: array
  }
}
```

### API接口设计

#### 1. 认证接口
```
POST /api/auth/setup     # 初始化API密钥
POST /api/auth/test      # 测试连接
PUT  /api/auth/toggle    # 切换模拟盘/实盘
```

#### 2. 交易接口
```
GET  /api/trading/account        # 获取账户信息
GET  /api/trading/positions      # 获取持仓
POST /api/trading/open           # 开仓
POST /api/trading/close          # 平仓
PUT  /api/trading/stop-loss      # 更新止损
```

#### 3. 市场数据接口
```
GET  /api/market/symbols         # 获取交易对列表
GET  /api/market/data/:symbol    # 获取市场数据
GET  /api/market/indicators/:symbol  # 获取技术指标
```

#### 4. AI决策接口
```
POST /api/ai/analyze             # AI分析请求
GET  /api/ai/decisions           # 获取决策历史
GET  /api/ai/confidence          # 获取当前置信度
```

#### 5. 系统接口
```
GET  /api/system/status          # 系统状态
GET  /api/system/logs            # 系统日志
POST /api/system/settings        # 更新设置
GET  /api/system/health          # 健康检查
```

## 实时数据处理和任务调度设计

### 1. 任务调度器架构
```javascript
class TaskScheduler {
  constructor() {
    this.tasks = new Map();
    this.intervals = new Map();
  }

  // 注册定时任务
  registerTask(name, handler, interval) {
    this.tasks.set(name, handler);
    const intervalId = setInterval(async () => {
      try {
        await handler();
      } catch (error) {
        logger.error(`Task ${name} failed:`, error);
      }
    }, interval);
    this.intervals.set(name, intervalId);
  }

  // 核心任务定义
  initializeTasks() {
    // 市场数据更新 - 每30秒
    this.registerTask('updateMarketData', this.updateMarketData.bind(this), 30000);

    // 技术指标计算 - 每分钟
    this.registerTask('calculateIndicators', this.calculateIndicators.bind(this), 60000);

    // AI决策分析 - 每2分钟
    this.registerTask('aiAnalysis', this.aiAnalysis.bind(this), 120000);

    // 持仓监控 - 每30秒
    this.registerTask('monitorPositions', this.monitorPositions.bind(this), 30000);

    // 风险检查 - 每10秒
    this.registerTask('riskCheck', this.riskCheck.bind(this), 10000);
  }
}
```

### 2. 数据流处理管道
```javascript
class DataPipeline {
  async processMarketData(symbol) {
    // 1. 获取原始数据
    const rawData = await this.dataService.getMultiTimeframeData(symbol);

    // 2. 数据验证和清洗
    const cleanData = this.validateAndCleanData(rawData);

    // 3. 更新缓存
    await this.cacheService.updateMarketData(symbol, cleanData);

    // 4. 计算技术指标
    const indicators = await this.indicatorService.calculateMultiTimeframeIndicators(cleanData);

    // 5. 更新指标缓存
    await this.cacheService.updateIndicators(symbol, indicators);

    // 6. 触发AI分析
    this.eventEmitter.emit('indicatorsUpdated', { symbol, indicators });

    return { data: cleanData, indicators };
  }

  validateAndCleanData(data) {
    // 数据完整性检查
    // 异常值检测和处理
    // 时间序列连续性验证
    return data;
  }
}
```

### 3. 事件驱动架构
```javascript
class EventManager extends EventEmitter {
  constructor() {
    super();
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // 市场数据更新事件
    this.on('marketDataUpdated', this.handleMarketDataUpdate.bind(this));

    // 技术指标更新事件
    this.on('indicatorsUpdated', this.handleIndicatorsUpdate.bind(this));

    // AI决策事件
    this.on('aiDecisionMade', this.handleAIDecision.bind(this));

    // 交易执行事件
    this.on('tradeExecuted', this.handleTradeExecution.bind(this));

    // 风险警告事件
    this.on('riskWarning', this.handleRiskWarning.bind(this));
  }

  async handleAIDecision(decision) {
    if (decision.confidence >= this.confidenceThreshold) {
      if (decision.action !== 'hold') {
        await this.tradingService.executeDecision(decision);
      }
    }
  }
}
```

### 4. 缓存管理策略
```javascript
class CacheManager {
  constructor() {
    this.marketDataCache = new Map();
    this.indicatorCache = new Map();
    this.maxCacheSize = 1000; // 最大缓存条目数
    this.ttl = 300000; // 5分钟TTL
  }

  set(key, value, customTTL = null) {
    const ttl = customTTL || this.ttl;
    const expiry = Date.now() + ttl;

    this.marketDataCache.set(key, {
      value,
      expiry
    });

    // 缓存大小控制
    if (this.marketDataCache.size > this.maxCacheSize) {
      this.evictOldest();
    }
  }

  get(key) {
    const item = this.marketDataCache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.marketDataCache.delete(key);
      return null;
    }

    return item.value;
  }

  evictOldest() {
    const oldestKey = this.marketDataCache.keys().next().value;
    this.marketDataCache.delete(oldestKey);
  }
}
```

## 数据模型设计

**重要说明：本系统采用"数据库配置优先"的设计理念，所有配置信息均存储在SQLite3数据库中，不使用传统的配置文件方式。这种设计具有以下优势：**

1. **统一管理：** 所有配置信息集中在数据库中，便于统一管理和维护
2. **动态更新：** 支持运行时配置更新，无需重启服务
3. **历史追踪：** 通过时间戳字段记录配置变更历史
4. **安全性：** 敏感信息可在数据库层面进行加密存储
5. **一致性：** 避免配置文件与数据库数据不一致的问题

### SQLite3 数据库表结构

#### 1. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
  id INTEGER PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  encrypted BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 交易参数表 (trading_params)
```sql
CREATE TABLE trading_params (
  id INTEGER PRIMARY KEY,
  max_leverage REAL NOT NULL DEFAULT 10,
  max_position_percent REAL NOT NULL DEFAULT 50,
  stop_loss_percent REAL NOT NULL DEFAULT 2,
  take_profit_percent REAL NOT NULL DEFAULT 4,
  confidence_threshold REAL NOT NULL DEFAULT 0.7,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 交易对配置表 (trading_pairs)
```sql
CREATE TABLE trading_pairs (
  id INTEGER PRIMARY KEY,
  symbol TEXT NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  min_notional REAL,
  tick_size REAL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. AI提示词模板表 (ai_prompts)
```sql
CREATE TABLE ai_prompts (
  id INTEGER PRIMARY KEY,
  type TEXT NOT NULL, -- 'opening' or 'position'
  template TEXT NOT NULL,
  version INTEGER DEFAULT 1,
  active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 内存数据结构 (临时缓存，不持久化)

#### 1. 市场数据临时缓存 (仅保留最近数据用于计算)
```javascript
{
  [symbol]: {
    '1m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '5m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '15m': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    '1h': [...recentOhlcvData], // 仅保留最近100条用于指标计算
    lastUpdate: timestamp,
    maxLength: 100 // 限制缓存长度，超出自动删除最旧数据
  }
}
```

#### 2. 技术指标临时缓存 (计算结果临时存储)
```javascript
{
  [symbol]: {
    [timeframe]: {
      sma: [...recentValues], // 仅保留最近计算结果
      ema: [...recentValues],
      rsi: [...recentValues],
      macd: {...recentMacdData},
      bollinger: {...recentBollingerData},
      lastCalculated: timestamp,
      maxAge: 300000 // 5分钟后自动失效
    }
  }
}
```

**注意：** 所有缓存数据都是临时的，系统重启后清空。历史数据完全依赖CCXT实时获取，不进行任何持久化存储。

## AI决策引擎详细设计

### 1. 开仓引擎提示词模板
```
你是一个专业的加密货币量化交易分析师。请基于以下技术指标数据分析当前市场状况，并给出开仓建议。

**市场数据:**
- 交易对: {symbol}
- 当前价格: {currentPrice}
- 24小时涨跌幅: {change24h}%

**多时间周期技术指标:**
1分钟周期: {indicators_1m}
5分钟周期: {indicators_5m}
15分钟周期: {indicators_15m}
1小时周期: {indicators_1h}

**账户信息:**
- 可用资金: {availableBalance} USDT
- 当前持仓: {currentPositions}

请分析并返回JSON格式的建议:
{
  "action": "long|short|hold",
  "confidence": 0.0-1.0,
  "reasoning": "详细分析理由",
  "entry_price": "建议入场价格",
  "stop_loss": "止损价格",
  "take_profit": "止盈价格"
}
```

### 2. 持仓引擎提示词模板
```
你是一个专业的加密货币持仓管理分析师。请基于当前持仓状况和最新市场数据，给出持仓管理建议。

**当前持仓信息:**
- 交易对: {symbol}
- 持仓方向: {side}
- 持仓数量: {size}
- 开仓价格: {entryPrice}
- 当前价格: {currentPrice}
- 未实现盈亏: {unrealizedPnl}
- 持仓时间: {holdingTime}

**最新技术指标:**
{latestIndicators}

**风险参数:**
- 当前止损: {currentStopLoss}
- 当前止盈: {currentTakeProfit}
- 最大回撤: {maxDrawdown}

请分析并返回JSON格式的建议:
{
  "action": "hold|close|adjust_stop|adjust_target",
  "confidence": 0.0-1.0,
  "reasoning": "详细分析理由",
  "new_stop_loss": "新止损价格(如需调整)",
  "new_take_profit": "新止盈价格(如需调整)"
}
```

### 3. AI响应解析和验证
```javascript
class AIResponseValidator {
  validateOpeningResponse(response) {
    const required = ['action', 'confidence', 'reasoning'];
    const validActions = ['long', 'short', 'hold'];

    // 验证必需字段
    for (const field of required) {
      if (!response[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // 验证动作类型
    if (!validActions.includes(response.action)) {
      throw new Error(`Invalid action: ${response.action}`);
    }

    // 验证置信度范围
    if (response.confidence < 0 || response.confidence > 1) {
      throw new Error(`Invalid confidence: ${response.confidence}`);
    }

    return true;
  }

  validatePositionResponse(response) {
    const validActions = ['hold', 'close', 'adjust_stop', 'adjust_target'];

    if (!validActions.includes(response.action)) {
      throw new Error(`Invalid position action: ${response.action}`);
    }

    return true;
  }
}
```

### 4. 技术指标数据格式化
```javascript
class IndicatorFormatter {
  formatForAI(indicators, timeframe) {
    return {
      trend: {
        sma_20: indicators.sma20[indicators.sma20.length - 1],
        sma_50: indicators.sma50[indicators.sma50.length - 1],
        ema_12: indicators.ema12[indicators.ema12.length - 1],
        ema_26: indicators.ema26[indicators.ema26.length - 1]
      },
      momentum: {
        rsi: indicators.rsi[indicators.rsi.length - 1],
        macd: {
          macd: indicators.macd.MACD[indicators.macd.MACD.length - 1],
          signal: indicators.macd.signal[indicators.macd.signal.length - 1],
          histogram: indicators.macd.histogram[indicators.macd.histogram.length - 1]
        }
      },
      volatility: {
        bollinger: {
          upper: indicators.bollinger.upper[indicators.bollinger.upper.length - 1],
          middle: indicators.bollinger.middle[indicators.bollinger.middle.length - 1],
          lower: indicators.bollinger.lower[indicators.bollinger.lower.length - 1]
        },
        atr: indicators.atr[indicators.atr.length - 1]
      },
      volume: {
        volume_sma: indicators.volumeSMA[indicators.volumeSMA.length - 1],
        current_volume: indicators.volume[indicators.volume.length - 1]
      }
    };
  }
}
```

## 错误处理设计

### 1. 分层错误处理策略

#### API层错误处理
```javascript
// 统一错误响应格式
{
  success: false,
  error: {
    code: 'ERROR_CODE',
    message: '用户友好的错误信息',
    details: '技术详细信息',
    timestamp: '2025-01-08T14:40:00Z'
  }
}
```

#### 服务层错误处理
```javascript
class CustomError extends Error {
  constructor(code, message, details = null) {
    super(message);
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}
```

### 2. 错误类型定义

- **EXCHANGE_ERROR:** 交易所API错误
- **AI_ERROR:** AI服务错误
- **VALIDATION_ERROR:** 数据验证错误
- **RISK_ERROR:** 风险控制错误
- **DATABASE_ERROR:** 数据库操作错误
- **NETWORK_ERROR:** 网络连接错误

### 3. 错误恢复机制

- **重试机制:** 指数退避重试策略
- **熔断器:** 防止级联故障
- **降级服务:** 关键功能的备用方案
- **告警通知:** 严重错误的实时通知

## 测试策略

### 1. 单元测试
- **覆盖率目标:** 80%以上
- **测试框架:** Jest + Supertest
- **真实API测试:** 使用提供的真实API密钥进行测试，包括欧易模拟盘API和DeepSeek API

### 2. 集成测试
- **API接口测试:** 完整的请求-响应流程
- **数据库测试:** SQLite操作的完整性
- **真实外部服务测试:** 使用真实的欧易模拟盘API和DeepSeek API进行集成测试

### 3. 端到端测试
- **真实交易流程测试:** 使用欧易模拟盘进行从配置到交易的完整流程测试
- **真实AI决策测试:** 使用DeepSeek API进行完整的AI决策流程测试
- **性能测试:** 真实API环境下的高频数据处理性能验证
- **压力测试:** 真实API限制下的系统负载能力测试

### 4. 真实API测试环境配置
- **欧易模拟盘配置:** 使用提供的OKX模拟盘API密钥进行真实交易测试
- **DeepSeek API配置:** 使用提供的DeepSeek API密钥进行真实AI决策测试
- **测试数据隔离:** 确保测试数据与生产数据完全隔离
- **API限制处理:** 处理真实API的频率限制和错误响应

### 5. 安全测试
- **API密钥安全:** 真实密钥的加密存储和传输测试
- **输入验证:** SQL注入、XSS防护测试
- **权限控制:** 访问控制和身份验证测试
- **真实API安全:** 验证与真实API通信的安全性

## 安全设计

### 1. API密钥管理
- **数据库加密存储:** 所有API密钥存储在SQLite3数据库中，使用AES-256-GCM加密算法
- **无配置文件暴露:** 不使用配置文件存储敏感信息，避免文件泄露风险
- **环境隔离:** 模拟盘和实盘密钥在数据库中分离存储
- **访问控制:** 密钥访问需要系统级认证
- **定期轮换:** 支持API密钥的定期更新，变更记录存储在数据库中

### 2. 数据传输安全
- **HTTPS强制:** 所有API通信使用HTTPS
- **请求签名:** 关键操作使用HMAC签名验证
- **速率限制:** API调用频率限制防止滥用
- **CORS配置:** 严格的跨域资源共享策略

### 3. 输入验证和防护
- **参数验证:** 所有输入参数的类型和范围验证
- **SQL注入防护:** 使用参数化查询
- **XSS防护:** 输出内容的HTML转义
- **CSRF防护:** 跨站请求伪造防护令牌

## 性能优化设计

### 1. 数据缓存策略
- **数据库配置缓存:** 系统启动时将数据库配置加载到内存，避免频繁数据库查询
- **多级缓存:** 内存缓存 + Redis缓存（可选）
- **缓存失效:** 基于时间和事件的缓存失效机制
- **预加载:** 常用数据的预加载策略
- **实时获取:** 所有市场数据通过CCXT实时获取，不进行本地存储
- **配置热更新:** 配置变更时自动更新内存缓存，无需重启服务

### 2. API优化
- **连接池:** 数据库连接池管理
- **请求合并:** 批量API请求减少网络开销
- **异步处理:** 非阻塞的异步操作
- **负载均衡:** 多实例部署的负载均衡

### 3. 前端性能优化
- **代码分割:** React组件的懒加载
- **虚拟滚动:** 大数据列表的虚拟滚动
- **状态优化:** Redux状态的规范化存储
- **缓存策略:** 静态资源的浏览器缓存

## 监控和日志设计

### 1. 系统监控指标
- **性能指标:** CPU、内存、网络使用率
- **业务指标:** 交易成功率、AI决策准确率
- **错误指标:** 错误率、响应时间
- **资源指标:** 数据库连接数、缓存命中率

### 2. 日志分级和格式
```javascript
// 日志级别
const LOG_LEVELS = {
  ERROR: 0,   // 系统错误
  WARN: 1,    // 警告信息
  INFO: 2,    // 一般信息
  DEBUG: 3,   // 调试信息
  TRACE: 4    // 详细跟踪
};

// 日志格式
{
  timestamp: '2025-01-08T14:40:00.000Z',
  level: 'INFO',
  module: 'TradingService',
  message: '开仓成功',
  data: {
    symbol: 'BTC-USDT-SWAP',
    side: 'long',
    size: 0.1,
    price: 45000
  },
  traceId: 'uuid-trace-id'
}
```

### 3. 告警机制
- **实时告警:** 关键错误的即时通知
- **阈值告警:** 指标超过预设阈值的告警
- **趋势告警:** 基于趋势分析的预警
- **恢复通知:** 故障恢复的确认通知

## 部署和运维设计

### 1. 部署架构
- **单机部署:** 开发和小规模生产环境
- **集群部署:** 高可用生产环境
- **容器化:** Docker容器化部署（可选）
- **自动化:** CI/CD自动化部署流程

### 2. 配置管理
- **数据库配置:** 所有配置信息统一存储在SQLite3数据库中，不使用配置文件
- **环境隔离:** 通过数据库中的环境标识区分开发、测试、生产环境配置
- **动态配置:** 运行时从数据库读取配置，支持热更新
- **版本控制:** 数据库表中的时间戳字段记录配置变更历史
- **备份恢复:** 数据库级别的配置和数据备份恢复策略

### 3. 运维工具
- **健康检查:** 系统健康状态检查接口
- **性能分析:** 性能瓶颈分析工具
- **日志分析:** 日志聚合和分析工具
- **监控面板:** 实时监控仪表板

## 扩展性设计

### 1. 模块化扩展
- **插件架构:** 支持新交易所的插件式接入
- **策略扩展:** 支持自定义交易策略
- **指标扩展:** 支持新技术指标的动态加载
- **AI模型扩展:** 支持多种AI模型的切换

### 2. 数据扩展
- **多交易所:** 支持多个交易所的同时连接
- **多币种:** 支持更多加密货币交易对
- **多时间周期:** 支持更多时间周期的分析
- **实时分析:** 基于实时数据的深度技术分析

### 3. 功能扩展
- **实时策略:** 基于实时数据的策略优化功能
- **组合管理:** 多策略组合的管理
- **风险分析:** 更详细的风险分析报告
- **社交功能:** 策略分享和社区功能
