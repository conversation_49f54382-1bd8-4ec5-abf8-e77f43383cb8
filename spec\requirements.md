# 加密货币永续合约全自动量化系统 需求文档

## 功能概述
基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，通过技术指标分析和AI决策引擎实现智能开仓和持仓管理。系统采用Node.js后端，React+Ant Design前端，支持欧易交易所的模拟盘和实盘交易，使用SQLite3数据库存储配置信息。

## 需求列表

### 1. 交易所集成与数据获取
**用户故事：** 作为量化交易者，我希望系统能够连接欧易交易所并获取实时市场数据，以便进行技术分析和交易决策。

**验收标准：**
1. 当系统启动时，应该能够通过CCXT库连接到欧易交易所的REST API
2. 当需要市场数据时，系统应该能够获取1分钟、5分钟、15分钟、1小时的K线数据
3. 当获取数据时，系统应该包含开高低收价格和成交量信息
4. 当用户切换模拟盘/实盘时，系统应该能够无缝切换API端点
5. 当API调用失败时，系统应该有重试机制和错误处理

### 2. 技术指标计算引擎
**用户故事：** 作为量化交易者，我希望系统能够计算多种技术指标，以便为AI模型提供分析数据。

**验收标准：**
1. 当获取到市场数据时，系统应该使用technicalindicators库计算常用技术指标
2. 当计算指标时，系统应该支持多个时间周期的指标组合分析
3. 当指标计算完成时，系统应该将结果格式化为AI模型可理解的数据结构
4. 当指标数据不足时，系统应该等待足够的历史数据再进行计算
5. 当计算出现错误时，系统应该记录错误并继续处理其他指标

### 3. AI决策引擎集成
**用户故事：** 作为量化交易者，我希望系统能够将技术分析结果发送给DeepSeek AI模型并获得交易建议，以便做出智能交易决策。

**验收标准：**
1. 当技术指标计算完成时，系统应该将数据发送给DeepSeek API
2. 当调用AI模型时，系统应该使用不同的提示词区分开仓引擎和持仓引擎
3. 当AI返回结果时，系统应该解析置信度和交易理由
4. 当置信度达到设定阈值时，系统应该触发相应的交易操作
5. 当AI API调用失败时，系统应该有降级策略和错误处理

### 4. 开仓引擎
**用户故事：** 作为量化交易者，我希望系统能够根据AI分析结果自动开仓，以便抓住交易机会。

**验收标准：**
1. 当AI开仓引擎给出高置信度信号时，系统应该计算合适的仓位大小
2. 当开仓时，系统应该根据设置的最大杠杆和最大仓位限制进行风险控制
3. 当开仓时，系统应该支持多头和空头方向的单向持仓
4. 当开仓时，系统应该设置初始止盈止损价格
5. 当开仓失败时，系统应该记录错误并通知用户

### 5. 持仓引擎
**用户故事：** 作为量化交易者，我希望系统能够智能管理现有持仓，以便优化收益和控制风险。

**验收标准：**
1. 当有持仓时，AI持仓引擎应该定期分析是否需要调整仓位
2. 当持仓引擎建议平仓时，系统应该执行平仓操作
3. 当持仓引擎建议调整止盈止损时，系统应该更新订单
4. 当持仓达到止盈止损条件时，系统应该自动平仓
5. 当持仓管理出现异常时，系统应该有紧急平仓机制

### 6. 风险管理系统
**用户故事：** 作为量化交易者，我希望系统有完善的风险控制机制，以便保护我的资金安全。

**验收标准：**
1. 当计算仓位时，系统应该基于可用资金而非总资产进行计算
2. 当设置杠杆时，系统应该考虑杠杆放大对止盈止损的影响
3. 当总仓位超过设定限制时，系统应该拒绝新的开仓请求
4. 当账户余额不足时，系统应该停止交易并发出警告
5. 当检测到异常交易时，系统应该暂停自动交易

### 7. 前端管理界面 - 总体布局
**用户故事：** 作为量化交易者，我希望有一个基于Ant Design的现代化前端界面来监控和配置交易系统，以便直观高效地管理我的交易策略。

**验收标准：**
1. 当用户访问系统时，应该看到使用Ant Design Layout组件构建的响应式布局
2. 当用户导航时，应该有清晰的侧边栏菜单使用Ant Design Menu组件
3. 当显示数据时，应该使用Ant Design的主题色彩和设计规范
4. 当用户操作时，应该有统一的Ant Design交互反馈（Loading、Message、Notification）
5. 当界面适配时，应该支持桌面和平板设备的响应式设计

### 8. 前端管理界面 - 仪表板页面
**用户故事：** 作为量化交易者，我希望有一个信息丰富的仪表板页面，以便快速了解系统运行状态和交易概况。

**验收标准：**
1. 当用户进入仪表板时，应该看到使用Ant Design Card组件展示的关键指标卡片
2. 当显示账户信息时，应该使用Statistic组件展示总资产、可用资金、当前收益等
3. 当显示持仓信息时，应该使用Table组件展示当前持仓列表和盈亏状态
4. 当显示系统状态时，应该使用Badge和Tag组件显示连接状态、AI引擎状态等
5. 当显示实时数据时，应该有自动刷新机制和Progress组件显示数据更新进度

### 9. 前端管理界面 - 交易配置页面
**用户故事：** 作为量化交易者，我希望有一个详细的配置页面来设置交易参数，以便精确控制交易策略。

**验收标准：**
1. 当用户配置基础参数时，应该使用Form组件和相应的Input、InputNumber、Slider组件
2. 当用户设置风险参数时，应该使用带验证的表单项设置最大杠杆、最大仓位、止盈止损比例
3. 当用户选择交易对时，应该使用Select组件提供搜索和多选功能
4. 当用户保存配置时，应该使用Button组件和确认Modal进行二次确认
5. 当配置有误时，应该使用Form.Item的validateStatus和help属性显示错误信息

### 10. 前端管理界面 - API密钥管理页面
**用户故事：** 作为量化交易者，我希望有一个安全的API密钥管理页面，以便配置交易所连接信息。

**验收标准：**
1. 当用户输入API密钥时，应该使用Input.Password组件确保密钥安全显示
2. 当用户切换模拟盘/实盘时，应该使用Switch组件和明确的Alert提示
3. 当用户测试连接时，应该使用Button组件触发连接测试并显示结果
4. 当显示连接状态时，应该使用Result组件展示成功/失败状态和详细信息
5. 当保存密钥时，应该有加密提示和Popconfirm确认组件

### 11. 前端管理界面 - 交易监控页面
**用户故事：** 作为量化交易者，我希望有一个实时的交易监控页面，以便观察AI决策过程和交易执行情况。

**验收标准：**
1. 当显示AI分析结果时，应该使用Timeline组件展示决策过程和时间线
2. 当显示置信度时，应该使用Progress组件和颜色编码显示不同置信度等级
3. 当显示交易信号时，应该使用Tag组件区分买入/卖出/持有信号
4. 当显示技术指标时，应该使用Descriptions组件整齐展示各项指标数值
5. 当显示交易日志时，应该使用List组件和虚拟滚动处理大量日志数据

### 12. 前端管理界面 - 交易历史页面
**用户故事：** 作为量化交易者，我希望有一个详细的交易历史页面，以便分析交易表现和优化策略。

**验收标准：**
1. 当显示交易记录时，应该使用Table组件支持排序、筛选和分页功能
2. 当筛选交易记录时，应该使用DatePicker、Select等组件提供多维度筛选
3. 当显示盈亏统计时，应该使用Statistic组件和颜色编码显示正负收益
4. 当导出数据时，应该使用Button组件和Dropdown提供多种导出格式选项
5. 当显示交易详情时，应该使用Modal组件展示单笔交易的详细信息

### 13. 前端管理界面 - 系统日志页面
**用户故事：** 作为量化交易者，我希望有一个系统日志页面来查看系统运行日志，以便排查问题和监控系统健康状态。

**验收标准：**
1. 当显示日志时，应该使用Table组件支持实时更新和虚拟滚动
2. 当筛选日志时，应该使用Select组件按日志级别（Error、Warning、Info）筛选
3. 当搜索日志时，应该使用Input.Search组件支持关键词搜索
4. 当显示日志详情时，应该使用Drawer组件展示完整的日志信息和堆栈跟踪
5. 当清理日志时，应该使用Popconfirm组件确认删除操作

### 14. 前端管理界面 - 响应式设计和用户体验
**用户故事：** 作为量化交易者，我希望前端界面具有优秀的用户体验和响应式设计，以便在不同设备上都能流畅使用。

**验收标准：**
1. 当在不同屏幕尺寸下使用时，应该使用Ant Design的Grid系统实现响应式布局
2. 当加载数据时，应该使用Skeleton组件提供优雅的加载状态
3. 当操作成功/失败时，应该使用Message和Notification组件提供即时反馈
4. 当网络异常时，应该使用Empty组件和重试按钮提供友好的错误处理
5. 当界面主题切换时，应该支持Ant Design的暗色主题模式

### 15. 配置和数据管理
**用户故事：** 作为量化交易者，我希望系统能够安全存储我的配置信息，以便系统稳定运行。

**验收标准：**
1. 当用户首次使用时，系统应该引导设置API密钥和交易参数
2. 当存储敏感信息时，系统应该使用SQLite3数据库加密存储
3. 当系统重启时，应该能够从数据库恢复所有配置信息
4. 当用户修改配置时，系统应该实时更新数据库
5. 当数据库操作失败时，系统应该有备份和恢复机制

### 16. 系统监控和日志
**用户故事：** 作为量化交易者，我希望系统能够提供详细的运行日志和监控信息，以便了解系统运行状态和排查问题。

**验收标准：**
1. 当系统运行时，应该记录所有关键操作的日志
2. 当发生错误时，应该记录详细的错误信息和堆栈跟踪
3. 当用户查看日志时，应该能够按时间和类型筛选日志
4. 当系统性能异常时，应该记录性能指标和警告
5. 当日志文件过大时，应该有自动轮转和清理机制

### 17. 全仓保证金交易模式
**用户故事：** 作为量化交易者，我希望系统支持全仓保证金交易模式，以便最大化资金利用效率。

**验收标准：**
1. 当开仓时，系统应该使用全仓模式进行保证金计算
2. 当计算可用保证金时，系统应该考虑所有持仓的总体风险
3. 当保证金不足时，系统应该自动调整仓位或拒绝开仓
4. 当强制平仓风险增加时，系统应该发出警告并考虑减仓
5. 当账户接近爆仓时，系统应该有紧急风控措施
